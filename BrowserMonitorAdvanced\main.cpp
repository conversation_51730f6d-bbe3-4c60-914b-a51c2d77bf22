#define _CRT_SECURE_NO_WARNINGS
#include <Windows.h>
#include <UIAutomation.h>
#include <AtlBase.h>
#include <AtlCom.h>
#include <iostream>
#include <string>
#include <sstream>
#include <vector>
#include <map>
#include <chrono>
#include <thread>
#include <algorithm>
#ifdef _WIN32
#define NOMINMAX
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#undef min
#undef max
#endif

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "oleaut32.lib")

// 全局变量
HHOOK g_hMouseHook = nullptr;
HHOOK g_hKeyboardHook = nullptr;
std::string g_lastUrl;
std::string g_currentTitle;



// 宽字符转换为UTF-8
std::string wstringToUtf8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}



// URL解码函数
std::string urlDecode(const std::string& encoded) {
    std::string decoded;
    for (size_t i = 0; i < encoded.length(); ++i) {
        if (encoded[i] == '%' && i + 2 < encoded.length()) {
            int value;
            std::istringstream is(encoded.substr(i + 1, 2));
            if (is >> std::hex >> value) {
                decoded += static_cast<char>(value);
                i += 2;
            } else {
                decoded += encoded[i];
            }
        } else if (encoded[i] == '+') {
            decoded += ' ';
        } else {
            decoded += encoded[i];
        }
    }
    return decoded;
}

// 提取关键词
std::vector<std::string> extractKeywords(const std::string& url) {
    std::vector<std::string> keywords;
    
    std::map<std::string, std::string> searchParams = {
        {"q=", ""},
        {"wd=", ""},
        {"query=", ""},
        {"search=", ""},
        {"s=", ""}
    };
    
    for (const auto& param : searchParams) {
        size_t pos = url.find(param.first);
        if (pos != std::string::npos) {
            size_t start = pos + param.first.length();
            size_t end = url.find('&', start);
            if (end == std::string::npos) end = url.length();
            
            std::string keyword = url.substr(start, end - start);
            if (!keyword.empty()) {
                keyword = urlDecode(keyword);
                keywords.push_back(keyword);
            }
        }
    }
    
    return keywords;
}

// 检查是否为浏览器窗口
bool isBrowserWindow(HWND hwnd) {
    char className[256];
    if (!GetClassNameA(hwnd, className, sizeof(className))) {
        return false;
    }

    // 检查是否为Chrome或Firefox窗口
    return (strcmp(className, "Chrome_WidgetWin_1") == 0 ||
            strcmp(className, "MozillaWindowClass") == 0);
}

// 清理URL
std::string cleanUrl(const std::string& url) {
    if (url.empty()) return url;
    
    size_t protocolEnd = url.find("://");
    if (protocolEnd == std::string::npos) return url;
    
    size_t domainStart = protocolEnd + 3;
    size_t pathStart = url.find('/', domainStart);
    size_t queryStart = url.find('?', domainStart);
    
    size_t end = std::min(pathStart, queryStart);
    if (pathStart != std::string::npos && queryStart != std::string::npos) {
        end = std::min(pathStart, queryStart);
    } else if (pathStart != std::string::npos) {
        end = pathStart;
    } else if (queryStart != std::string::npos) {
        end = queryStart;
    } else {
        end = url.length();
    }
    
    std::string result = url.substr(domainStart, end - domainStart);
    if (pathStart != std::string::npos && pathStart < queryStart) {
        size_t pathEnd = (queryStart != std::string::npos) ? queryStart : url.length();
        result += url.substr(pathStart, pathEnd - pathStart);
    }
    
    return result;
}

// 输出格式化信息
void outputUrlInfo(const std::string& url, const std::string& title) {
    if (url.empty()) return;

    std::cout << "找到地址和搜索栏" << std::endl;
    std::cout << "[地址栏当前内容]: " << url << std::endl;

    auto keywords = extractKeywords(url);
    std::string cleanedUrl = cleanUrl(url);

    std::cout << "[数据清理输出成JSON格式]:" << std::endl;
    std::cout << "{" << std::endl;
    std::cout << "  \"keywords\": [";

    for (size_t i = 0; i < keywords.size(); ++i) {
        std::cout << "\"" << keywords[i] << "\"";
        if (i < keywords.size() - 1) std::cout << ", ";
    }

    std::cout << "]," << std::endl;
    std::cout << "  \"url\": \"" << cleanedUrl << "\"" << std::endl;
    std::cout << "}" << std::endl;

    if (!title.empty()) {
        std::cout << "[Tab 标题]: " << title << std::endl;
    }
    std::cout << std::endl;
}



// 使用UI Automation获取Chrome地址栏URL
std::string getChromeUrlWithUIAutomation(HWND hwnd) {


    // 在当前线程中初始化COM
    HRESULT hrInit = CoInitialize(nullptr);

    CComPtr<IUIAutomation> pAutomation;
    HRESULT hr = CoCreateInstance(CLSID_CUIAutomation, NULL, CLSCTX_INPROC_SERVER,
                                  IID_IUIAutomation, (void**)&pAutomation);
    if (FAILED(hr) || !pAutomation) {
        std::cout << "创建UI Automation失败，错误代码: " << std::hex << hr << std::endl;
        if (SUCCEEDED(hrInit)) CoUninitialize();
        return "";
    }

    CComPtr<IUIAutomationElement> pRootElement;
    hr = pAutomation->ElementFromHandle(hwnd, &pRootElement);
    if (FAILED(hr) || !pRootElement) {
        std::cout << "从窗口句柄获取元素失败" << std::endl;
        return "";
    }
    
    // 查找地址栏


    CComPtr<IUIAutomationCondition> pCondition;

    // 方式: 按控件类型查找Edit控件
    VARIANT varProp;
    varProp.vt = VT_I4;
    varProp.lVal = UIA_EditControlTypeId;
    hr = pAutomation->CreatePropertyCondition(UIA_ControlTypePropertyId, varProp, &pCondition);

    if (SUCCEEDED(hr) && pCondition) {
        // 查找所有Edit控件
        CComPtr<IUIAutomationElementArray> pElements;
        hr = pRootElement->FindAll(TreeScope_Descendants, pCondition, &pElements);

        if (SUCCEEDED(hr) && pElements) {
            int length = 0;
            pElements->get_Length(&length);

            // 首先查找有焦点的Edit控件
            for (int i = 0; i < length; i++) {
                CComPtr<IUIAutomationElement> pElement;
                hr = pElements->GetElement(i, &pElement);

                if (SUCCEEDED(hr) && pElement) {
                    // 检查是否有焦点
                    BOOL hasFocus = FALSE;
                    pElement->get_CurrentHasKeyboardFocus(&hasFocus);

                    if (hasFocus) {
                        VARIANT varValue;
                        VariantInit(&varValue);
                        hr = pElement->GetCurrentPropertyValue(UIA_ValueValuePropertyId, &varValue);

                        if (SUCCEEDED(hr) && varValue.vt == VT_BSTR && varValue.bstrVal) {
                            std::string value = wstringToUtf8(std::wstring(varValue.bstrVal));
                            if (!value.empty()) {
                                VariantClear(&varValue);
                                if (SUCCEEDED(hrInit)) CoUninitialize();
                                return value;
                            }
                        }
                        VariantClear(&varValue);
                    }
                }
            }

            // 如果没有找到有焦点的，查找名称匹配地址栏的Edit控件
            for (int i = 0; i < length; i++) {
                CComPtr<IUIAutomationElement> pElement;
                hr = pElements->GetElement(i, &pElement);

                if (SUCCEEDED(hr) && pElement) {
                    BSTR name = nullptr;
                    pElement->get_CurrentName(&name);

                    if (name) {
                        std::wstring elementName(name);
                        std::wstring lowerName = elementName;
                        std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::towlower);

                        if (lowerName.find(L"address") != std::wstring::npos ||
                            lowerName.find(L"地址") != std::wstring::npos ||
                            lowerName.find(L"url") != std::wstring::npos ||
                            lowerName.find(L"location") != std::wstring::npos) {

                            VARIANT varValue;
                            VariantInit(&varValue);
                            hr = pElement->GetCurrentPropertyValue(UIA_ValueValuePropertyId, &varValue);

                            if (SUCCEEDED(hr) && varValue.vt == VT_BSTR && varValue.bstrVal) {
                                std::string url = wstringToUtf8(std::wstring(varValue.bstrVal));
                                VariantClear(&varValue);
                                SysFreeString(name);
                                if (SUCCEEDED(hrInit)) CoUninitialize();
                                return url;
                            }
                            VariantClear(&varValue);
                        }
                        SysFreeString(name);
                    }
                }
            }

            // 最后，查找包含URL特征的Edit控件
            for (int i = 0; i < length; i++) {
                CComPtr<IUIAutomationElement> pElement;
                hr = pElements->GetElement(i, &pElement);

                if (SUCCEEDED(hr) && pElement) {
                    VARIANT varValue;
                    VariantInit(&varValue);
                    hr = pElement->GetCurrentPropertyValue(UIA_ValueValuePropertyId, &varValue);

                    if (SUCCEEDED(hr) && varValue.vt == VT_BSTR && varValue.bstrVal) {
                        std::string value = wstringToUtf8(std::wstring(varValue.bstrVal));

                        // 检查是否包含URL特征
                        if (!value.empty() && (
                            value.find("http://") == 0 ||
                            value.find("https://") == 0 ||
                            value.find("www.") != std::string::npos ||
                            value.find(".com") != std::string::npos ||
                            value.find(".org") != std::string::npos ||
                            value.find(".net") != std::string::npos ||
                            (value.length() < 50 && value.find(" ") == std::string::npos))) { // 简单的搜索词

                            VariantClear(&varValue);
                            if (SUCCEEDED(hrInit)) CoUninitialize();
                            return value;
                        }
                    }
                    VariantClear(&varValue);
                }
            }
        }
    }
    if (SUCCEEDED(hrInit)) CoUninitialize();
    return "";
}

// 使用UI Automation获取Firefox地址栏URL
std::string getFirefoxUrlWithUIAutomation(HWND hwnd) {
    // 在当前线程中初始化COM
    HRESULT hrInit = CoInitialize(nullptr);

    CComPtr<IUIAutomation> pAutomation;
    HRESULT hr = CoCreateInstance(CLSID_CUIAutomation, NULL, CLSCTX_INPROC_SERVER,
                                  IID_IUIAutomation, (void**)&pAutomation);
    if (FAILED(hr) || !pAutomation) {
        if (SUCCEEDED(hrInit)) CoUninitialize();
        return "";
    }

    CComPtr<IUIAutomationElement> pRootElement;
    hr = pAutomation->ElementFromHandle(hwnd, &pRootElement);
    if (FAILED(hr) || !pRootElement) {
        if (SUCCEEDED(hrInit)) CoUninitialize();
        return "";
    }

    // Firefox特定的控件查找 - 查找所有Edit控件

    CComPtr<IUIAutomationCondition> pCondition;
    VARIANT varProp;
    varProp.vt = VT_I4;
    varProp.lVal = UIA_EditControlTypeId;
    hr = pAutomation->CreatePropertyCondition(UIA_ControlTypePropertyId, varProp, &pCondition);

    if (SUCCEEDED(hr) && pCondition) {
        CComPtr<IUIAutomationElementArray> pElements;
        hr = pRootElement->FindAll(TreeScope_Descendants, pCondition, &pElements);

        if (SUCCEEDED(hr) && pElements) {
            int length = 0;
            pElements->get_Length(&length);

            // 首先查找有焦点的Edit控件
            for (int i = 0; i < length; i++) {
                CComPtr<IUIAutomationElement> pElement;
                hr = pElements->GetElement(i, &pElement);

                if (SUCCEEDED(hr) && pElement) {
                    // 检查是否有焦点
                    BOOL hasFocus = FALSE;
                    pElement->get_CurrentHasKeyboardFocus(&hasFocus);

                    if (hasFocus) {
                        VARIANT varValue;
                        VariantInit(&varValue);
                        hr = pElement->GetCurrentPropertyValue(UIA_ValueValuePropertyId, &varValue);

                        if (SUCCEEDED(hr) && varValue.vt == VT_BSTR && varValue.bstrVal) {
                            std::string value = wstringToUtf8(std::wstring(varValue.bstrVal));
                            if (!value.empty()) {
                                VariantClear(&varValue);
                                if (SUCCEEDED(hrInit)) CoUninitialize();
                                return value;
                            }
                        }
                        VariantClear(&varValue);
                    }
                }
            }

            // 如果没有找到有焦点的，查找包含URL特征的Edit控件
            for (int i = 0; i < length; i++) {
                CComPtr<IUIAutomationElement> pElement;
                hr = pElements->GetElement(i, &pElement);

                if (SUCCEEDED(hr) && pElement) {
                    VARIANT varValue;
                    VariantInit(&varValue);
                    hr = pElement->GetCurrentPropertyValue(UIA_ValueValuePropertyId, &varValue);

                    if (SUCCEEDED(hr) && varValue.vt == VT_BSTR && varValue.bstrVal) {
                        std::string value = wstringToUtf8(std::wstring(varValue.bstrVal));

                        // 检查是否包含URL特征或者是简单的搜索词
                        if (!value.empty() && (
                            value.find("http://") == 0 ||
                            value.find("https://") == 0 ||
                            value.find("www.") != std::string::npos ||
                            value.find(".com") != std::string::npos ||
                            (value.length() < 50 && value.find(" ") == std::string::npos))) { // 简单的搜索词

                            VariantClear(&varValue);
                            if (SUCCEEDED(hrInit)) CoUninitialize();
                            return value;
                        }
                    }
                    VariantClear(&varValue);
                }
            }
        }
    }

    if (SUCCEEDED(hrInit)) CoUninitialize();
    return "";
}



// 鼠标钩子回调函数
LRESULT CALLBACK MouseHookProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0) {
        if (wParam == WM_LBUTTONUP) {
            POINT pt;
            GetCursorPos(&pt);
            HWND hwnd = WindowFromPoint(pt);

            // 找到顶级窗口
            while (GetParent(hwnd) != NULL) {
                hwnd = GetParent(hwnd);
            }

            if (isBrowserWindow(hwnd)) {
                std::thread([hwnd]() {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1500));

                    // 检查窗口类名并识别浏览器类型
                    char className[256];
                    if (!GetClassNameA(hwnd, className, sizeof(className))) {
                        return;
                    }

                    // 直接根据窗口类名获取URL
                    std::string newUrl;

                    if (strcmp(className, "Chrome_WidgetWin_1") == 0) {
                        newUrl = getChromeUrlWithUIAutomation(hwnd);
                    } else if (strcmp(className, "MozillaWindowClass") == 0) {
                        newUrl = getFirefoxUrlWithUIAutomation(hwnd);
                    } else {
                        return; // 不是支持的浏览器
                    }

                    // 获取窗口标题
                    wchar_t windowTitle[512];
                    GetWindowTextW(hwnd, windowTitle, sizeof(windowTitle) / sizeof(wchar_t));
                    std::string title = wstringToUtf8(std::wstring(windowTitle));

                    // 只有当URL为空或者（URL未变化且标题未变化）时才return
                    if (newUrl.empty() || (newUrl == g_lastUrl && title == g_currentTitle)) {
                        return; // URL为空或（URL未变化且标题未变化）
                    }

                    // 输出新URL信息
                    outputUrlInfo(newUrl, title);

                    // 更新当前URL和标题
                    g_lastUrl = newUrl;
                    g_currentTitle = title;
                }).detach();
            }
        }
    }

    return CallNextHookEx(g_hMouseHook, nCode, wParam, lParam);
}

// 键盘钩子回调函数
LRESULT CALLBACK KeyboardHookProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0) {
        if (wParam == WM_KEYUP) {
            KBDLLHOOKSTRUCT* pKeyboard = (KBDLLHOOKSTRUCT*)lParam;

            // 检测相关按键
            if (pKeyboard->vkCode == VK_RETURN ||
                pKeyboard->vkCode == VK_SPACE ||
                pKeyboard->vkCode == VK_BACK ||
                pKeyboard->vkCode == VK_DELETE ||
                (pKeyboard->vkCode >= 'A' && pKeyboard->vkCode <= 'Z') ||
                (pKeyboard->vkCode >= '0' && pKeyboard->vkCode <= '9')) {

                HWND hwnd = GetForegroundWindow();

                if (isBrowserWindow(hwnd)) {
                    std::thread([hwnd]() {
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));

                        // 检查窗口类名并识别浏览器类型
                        char className[256];
                        if (!GetClassNameA(hwnd, className, sizeof(className))) {
                            return;
                        }

                        // 直接根据窗口类名获取URL
                        std::string newUrl;

                        if (strcmp(className, "Chrome_WidgetWin_1") == 0) {
                            newUrl = getChromeUrlWithUIAutomation(hwnd);
                        } else if (strcmp(className, "MozillaWindowClass") == 0) {
                            newUrl = getFirefoxUrlWithUIAutomation(hwnd);
                        } else {
                            return; // 不是支持的浏览器
                        }

                        // 获取窗口标题
                        wchar_t windowTitle[512];
                        GetWindowTextW(hwnd, windowTitle, sizeof(windowTitle) / sizeof(wchar_t));
                        std::string title = wstringToUtf8(std::wstring(windowTitle));

                        // 只有当URL为空或者（URL未变化且标题未变化）时才return
                        if (newUrl.empty() || (newUrl == g_lastUrl && title == g_currentTitle)) {
                            return; // URL为空或（URL未变化且标题未变化）
                        }

                        // 输出新URL信息
                        outputUrlInfo(newUrl, title);

                        // 更新当前URL和标题
                        g_lastUrl = newUrl;
                        g_currentTitle = title;
                    }).detach();
                }
            }
        }
    }

    return CallNextHookEx(g_hKeyboardHook, nCode, wParam, lParam);
}

// 安装钩子
bool installHook() {
    CoInitialize(nullptr);

    // 安装鼠标钩子
    g_hMouseHook = SetWindowsHookEx(WH_MOUSE_LL, MouseHookProc, GetModuleHandle(NULL), 0);
    if (!g_hMouseHook) {
        std::cerr << "安装鼠标钩子失败" << std::endl;
        return false;
    }

    // 安装键盘钩子
    g_hKeyboardHook = SetWindowsHookEx(WH_KEYBOARD_LL, KeyboardHookProc, GetModuleHandle(NULL), 0);
    if (!g_hKeyboardHook) {
        std::cerr << "安装键盘钩子失败" << std::endl;
        UnhookWindowsHookEx(g_hMouseHook);
        g_hMouseHook = nullptr;
        return false;
    }

    return true;
}

// 卸载钩子
void uninstallHook() {
    if (g_hKeyboardHook) {
        UnhookWindowsHookEx(g_hKeyboardHook);
        g_hKeyboardHook = nullptr;
    }

    if (g_hMouseHook) {
        UnhookWindowsHookEx(g_hMouseHook);
        g_hMouseHook = nullptr;
    }

    CoUninitialize();
}

// 信号处理函数
BOOL WINAPI ConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT) {
        std::cout << "\n程序正在退出..." << std::endl;

        uninstallHook();

        exit(0);
        return TRUE;
    }
    return FALSE;
}

int main() {
    // 设置控制台标题和编码
    SetConsoleTitleA("Advanced Browser Monitor - Mouse & Keyboard Hooks + UI Automation");
    SetConsoleOutputCP(CP_UTF8);

    // 设置Ctrl+C处理
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);

    std::cout << "程序启动运行中......, 按Ctrl+C退出" << std::endl;
    std::cout << "支持浏览器: Chrome, Firefox" << std::endl;
    std::cout << "技术架构: 鼠标键盘钩子 + UI Automation" << std::endl;

    // 安装钩子
    if (!installHook()) {
        std::cerr << "无法安装钩子" << std::endl;
        return 1;
    }

    std::cout << "浏览器监控已启动，等待鼠标点击和键盘输入..." << std::endl;

    // 消息循环
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 清理
    uninstallHook();

    return 0;
}
